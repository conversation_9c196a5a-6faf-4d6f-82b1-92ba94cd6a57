<?php

namespace Tests\Feature\General\Reservation;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class UtilityTableResponsiveTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Field $field;
    private Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->active()->create();
        $this->utility = Utility::factory()->active()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 25.00,
        ]);
    }

    #[Test]
    public function reservation_create_form_has_responsive_utility_table_structure()
    {
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for responsive table wrapper
        $this->assertStringContainsString('table-responsive', $content);
        
        // Check for utility table container
        $this->assertStringContainsString('id="utilityTableContainer"', $content);
        
        // Check for utility table with proper classes
        $this->assertStringContainsString('table table-bordered text-nowrap', $content);
        
        // Check for utility table ID
        $this->assertStringContainsString('id="utilityTable"', $content);
        
        // Verify the table structure is properly nested
        $this->assertMatchesRegularExpression(
            '/<div[^>]*id="utilityTableContainer"[^>]*>.*?<div[^>]*class="[^"]*table-responsive[^"]*"[^>]*>.*?<table[^>]*id="utilityTable"[^>]*>/s',
            $content,
            'Utility table should be properly nested within responsive wrapper'
        );
    }

    #[Test]
    public function reservation_edit_form_has_responsive_utility_table_structure()
    {
        // Create a reservation to edit (more than 24 hours in the future to be modifiable)
        $reservation = \App\Models\Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'), // 3 days in future to ensure it's modifiable
            'status' => 'Confirmed',
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for responsive table wrapper
        $this->assertStringContainsString('table-responsive', $content);
        
        // Check for utility table container
        $this->assertStringContainsString('id="utilityTableContainer"', $content);
        
        // Check for utility table with proper classes
        $this->assertStringContainsString('table table-bordered text-nowrap', $content);
        
        // Check for utility table ID
        $this->assertStringContainsString('id="utilityTable"', $content);
        
        // Verify the table structure is properly nested
        $this->assertMatchesRegularExpression(
            '/<div[^>]*id="utilityTableContainer"[^>]*>.*?<div[^>]*class="[^"]*table-responsive[^"]*"[^>]*>.*?<table[^>]*id="utilityTable"[^>]*>/s',
            $content,
            'Utility table should be properly nested within responsive wrapper'
        );
    }

    #[Test]
    public function utility_table_has_proper_column_structure()
    {
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for all required table headers
        $this->assertStringContainsString('<th>Utility</th>', $content);
        $this->assertStringContainsString('<th>Quantity</th>', $content);
        $this->assertStringContainsString('<th>Rate</th>', $content);
        $this->assertStringContainsString('<th>Cost</th>', $content);
        $this->assertStringContainsString('<th>Action</th>', $content);
    }

    #[Test]
    public function utility_table_responsive_css_is_included()
    {
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for responsive CSS styles
        $this->assertStringContainsString('#utilityTableContainer', $content);
        $this->assertStringContainsString('overflow-x: auto', $content);
        $this->assertStringContainsString('-webkit-overflow-scrolling: touch', $content);
        
        // Check for mobile breakpoint styles
        $this->assertStringContainsString('@media (max-width: 768px)', $content);
        $this->assertStringContainsString('min-width: 500px', $content);
        
        // Check for extra small device styles
        $this->assertStringContainsString('@media (max-width: 576px)', $content);
        $this->assertStringContainsString('min-width: 450px', $content);
    }

    #[Test]
    public function utility_table_maintains_functionality_with_responsive_wrapper()
    {
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Verify JavaScript functionality is preserved
        $this->assertStringContainsString('addUtility()', $content);
        $this->assertStringContainsString('removeUtility(', $content);
        $this->assertStringContainsString('utilityTableBody', $content);
        $this->assertStringContainsString('utilityTableHeader', $content);
        
        // Verify utility selection interface is present
        $this->assertStringContainsString('utilitySelect', $content);
        $this->assertStringContainsString('utilityQuantity', $content);
        $this->assertStringContainsString('addUtilityBtn', $content);
    }
}
